"use client";

import * as React from "react";
import { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@workspace/ui/components/dropdown-menu";
import {
  ReceiptText,
  X,
  Filter,
  Search as SearchIcon,
  Download,
  Hourglass,
  Clock,
} from "lucide-react";
import { KYCDialog } from "./kyc.dialog";
import { IbanDialog } from "./iban.dialog";
import { BankTransferDialog } from "./bank-transfer.dialog";
import { WithdrawalDialog } from "./withdrawal.dialog";
import { useTransactions, useBalances } from "@/hooks/queries/transactions";
import { useUser } from "@/hooks/queries/user";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Search } from "@workspace/ui/components/search";
import { Badge } from "@workspace/ui/components/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@workspace/ui/components/alert-dialog";
import { useDialogHistory } from "@workspace/ui/hooks/use-dialog-history";

// Transaction type definitions
export interface Transaction {
  id: string;
  date: string;
  type: string;
  description: string;
  amount: number;
  newBalance: number;
  status?: string;
  isNew?: boolean;
}

export interface TransactionData {
  [month: string]: Transaction[];
}

export interface WalletDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onDepositClick?: () => void;
  onBankTransferClick?: () => void;
  onWithdrawClick?: () => void;
  onEarnClick?: () => void;
  historyKey?: string;
  children?: React.ReactNode;
}

export function WalletDialog({
  open = false,
  onOpenChange,
  onBankTransferClick,
  onWithdrawClick,
  onEarnClick,
  historyKey,
  children,
}: WalletDialogProps) {
  const effectiveHistoryKey =
    historyKey === "" ? undefined : (historyKey ?? "wallet");
  const { data: transactionData, isLoading: transactionsLoading } =
    useTransactions();
  const { data: balanceData, isLoading: balancesLoading } = useBalances();
  const { data: userData, isLoading: userLoading } = useUser();
  const [activeTab, setActiveTab] = useState("soda");
  const [selectedMonth, setSelectedMonth] = useState("2025-01");

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTransactionTypes, setSelectedTransactionTypes] = useState<
    string[]
  >([]);

  // Withdrawal deletion state
  const [deletingTransactionId, setDeletingTransactionId] = useState<
    string | null
  >(null);

  // Child dialog states with history support
  const [kycOpen, setKycOpen] = useDialogHistory(
    effectiveHistoryKey ? `${effectiveHistoryKey}-kyc` : "kyc",
  );
  const [bankTransferOpen, setBankTransferOpen] = useDialogHistory(
    effectiveHistoryKey
      ? `${effectiveHistoryKey}-bank-transfer`
      : "bank-transfer",
  );
  const [ibanOpen, setIbanOpen] = useDialogHistory(
    effectiveHistoryKey ? `${effectiveHistoryKey}-iban` : "iban",
  );
  const [withdrawalOpen, setWithdrawalOpen] = useDialogHistory(
    effectiveHistoryKey ? `${effectiveHistoryKey}-withdrawal` : "withdrawal",
  );

  // Child dialog handlers
  const handleBankTransferClick = () => {
    if (!userData) return;

    if (userData.is_kyc_approved) {
      setBankTransferOpen(true);
    } else {
      setKycOpen(true);
    }
    if (onBankTransferClick) onBankTransferClick();
  };

  const handleWithdrawClick = () => {
    if (!userData) return;

    if (userData.is_iban_saved) {
      setWithdrawalOpen(true);
    } else {
      setIbanOpen(true);
    }
    if (onWithdrawClick) onWithdrawClick();
  };

  const handleKycSubmit = () => {
    setKycOpen(false);
    setBankTransferOpen(true);
  };

  const handleIbanSubmit = () => {
    setIbanOpen(false);
    setWithdrawalOpen(true);
  };

  // CSV Export function
  const handleCsvExport = () => {
    const transactions = getCurrentTransactions();
    if (transactions.length === 0) {
      alert("Dışa aktarılacak işlem bulunamadı.");
      return;
    }

    // Create CSV content
    const headers = [
      "İşlem Tarihi",
      "İşlem No",
      "İşlem Türü",
      "Açıklama",
      "Miktar",
      "Yeni Bakiye",
    ];
    const csvContent = [
      headers.join(","),
      ...transactions.map((transaction) =>
        [
          transaction.date,
          transaction.id,
          transaction.type,
          `"${transaction.description}"`, // Wrap in quotes to handle commas
          transaction.amount,
          transaction.newBalance,
        ].join(","),
      ),
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `${activeTab}_transactions_${selectedMonth}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle withdrawal deletion
  const handleDeleteWithdrawal = (transactionId: string) => {
    console.log("Deleting withdrawal request:", transactionId);
    // TODO: Implement actual deletion logic here
    // This would typically involve calling an API to delete the withdrawal request
    setDeletingTransactionId(null);
  };

  // Generate months for current year
  const currentYear = new Date().getFullYear();
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    const monthStr = month.toString().padStart(2, "0");
    return {
      value: `${currentYear}-${monthStr}`,
      label: new Date(currentYear, i).toLocaleDateString("tr-TR", {
        month: "long",
        year: "numeric",
      }),
    };
  });

  const getCurrentTransactions = (): Transaction[] => {
    if (!transactionData) return [];

    let transactions: Transaction[] = [];
    if (activeTab === "soda") {
      transactions = transactionData.sodaTransactions[selectedMonth] || [];
    } else {
      transactions = transactionData.capsTransactions[selectedMonth] || [];
    }

    // Apply search filter
    if (searchQuery) {
      transactions = transactions.filter(
        (transaction) =>
          transaction.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          transaction.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          transaction.id.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Apply transaction type filter
    if (selectedTransactionTypes.length > 0) {
      transactions = transactions.filter((transaction) =>
        selectedTransactionTypes.includes(transaction.type),
      );
    }

    return transactions;
  };

  // Calculate balance for the selected month by finding the latest transaction's balance
  const getBalanceForMonth = (
    month: string,
    transactionData: TransactionData,
  ): number => {
    const transactions = transactionData[month] || [];
    if (transactions.length === 0) {
      // If no transactions for this month, find the latest transaction from previous months
      const allMonths = Object.keys(transactionData).sort();
      const currentMonthIndex = allMonths.indexOf(month);

      for (let i = currentMonthIndex - 1; i >= 0; i--) {
        const monthKey = allMonths[i];
        if (monthKey) {
          const prevMonthTransactions = transactionData[monthKey] || [];
          if (prevMonthTransactions.length > 0) {
            // Return the latest balance from the previous month
            const firstTransaction = prevMonthTransactions[0];
            return firstTransaction ? firstTransaction.newBalance : 0;
          }
        }
      }

      // If no previous transactions found, return 0
      return 0;
    }

    // Return the latest transaction's balance (first in array since they're sorted by date desc)
    const firstTransaction = transactions[0];
    return firstTransaction ? firstTransaction.newBalance : 0;
  };

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      historyKey={effectiveHistoryKey}
    >
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className="sm:max-w-[1000px] h-[80vh] max-h-[1000px] overflow-hidden w-full max-w-full flex flex-col pb-12"
      >
        <div className="w-full flex flex-col flex-1 pt-3">
          <DialogHeader className="flex-row justify-between gap-4">
            <DialogTitle variant="stripe" className="w-2/3 min-w-40">
              {"CÜZDAN"}
            </DialogTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mr-9 h-14 text-2xl font-mono font-bold">
                <TabsTrigger value="soda" className="w-50 relative first:pl-4">
                  <div className="flex items-center justify-between gap-3">
                    <div className="w-14 h-14 shrink-0 relative">
                      <Image
                        src="/assets/soda-lg.png"
                        alt="Soda"
                        fill
                        sizes="150px"
                        className="object-contain"
                      />
                    </div>
                    <div className="text-right">
                      {balancesLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        balanceData?.sodaBalance || 0
                      )}
                    </div>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="caps" className="w-50 relative pl-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="w-12 h-12 shrink-0 relative">
                      <Image
                        src="/assets/cap-lg.png"
                        alt="Cap"
                        fill
                        sizes="100px"
                        className="object-contain"
                      />
                    </div>
                    <div>
                      {balancesLoading ? (
                        <Skeleton className="h-6 w-16" />
                      ) : (
                        balanceData?.capsBalance || 0
                      )}
                    </div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </DialogHeader>

          <div className="flex flex-col flex-1 gap-6 mx-10 mt-6">
            {/* Month selector and action buttons */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger className="w-45">
                    <SelectValue placeholder="Ay seçin" />
                  </SelectTrigger>
                  <SelectContent className="w-45">
                    {months.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Filter Button */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="icon"
                      variant={
                        selectedTransactionTypes.length > 0
                          ? "secondary"
                          : "default"
                      }
                      className={
                        selectedTransactionTypes.length > 0
                          ? "bg-primary text-primary-foreground"
                          : ""
                      }
                    >
                      <Filter className="h-4 w-4 fill-background" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-40" align="start">
                    <DropdownMenuLabel>İşlem Türleri</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {[
                      "Yatırım",
                      "Çekim",
                      "Ödeme",
                      "Kazanç",
                      "Hediye",
                      "Gönderim",
                      "Talep",
                    ].map((type) => (
                      <DropdownMenuCheckboxItem
                        key={type}
                        checked={selectedTransactionTypes.includes(type)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedTransactionTypes([
                              ...selectedTransactionTypes,
                              type,
                            ]);
                          } else {
                            setSelectedTransactionTypes(
                              selectedTransactionTypes.filter(
                                (t) => t !== type,
                              ),
                            );
                          }
                        }}
                      >
                        {type}
                      </DropdownMenuCheckboxItem>
                    ))}
                    {selectedTransactionTypes.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => setSelectedTransactionTypes([])}
                          className="justify-center"
                        >
                          {"Temizle"}
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Search Button */}
                <Search
                  value={searchQuery}
                  onValueChange={setSearchQuery}
                  placeholder="İşlem açıklaması ara..."
                  size="icon"
                  align="start"
                  className="w-72"
                />

                {/* Download Button */}
                <Button onClick={handleCsvExport} size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex gap-3">
                {activeTab === "soda" ? (
                  <>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="small"
                          variant="default"
                          className="w-24 flex items-center gap-2"
                          disabled={userLoading}
                        >
                          {"YATIR"}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={handleBankTransferClick}>
                          Banka Transferi
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          Kredi Kartı
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                      onClick={handleWithdrawClick}
                      size="small"
                      variant="default"
                      className="w-24"
                      disabled={userLoading}
                    >
                      {"ÇEK"}
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={onEarnClick}
                      disabled
                      size="small"
                      variant="default"
                      className="w-24"
                    >
                      {"KAZAN"}
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Transactions table */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0">
                    Tarih
                  </TableHead>
                  <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                    No
                  </TableHead>
                  <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                    Tür
                  </TableHead>
                  <TableHead className="flex-1 min-w-0">Açıklama</TableHead>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                    Miktar
                  </TableHead>
                  <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                    Bakiye
                  </TableHead>
                  <TableHead className="w-12 min-w-[3rem] flex-shrink-0"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactionsLoading ? (
                  // Skeleton loading rows
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-8 w-8" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : getCurrentTransactions().length === 0 ? (
                  <TableRow className="hover:bg-transparent">
                    <TableCell
                      colSpan={7}
                      className="text-center text-muted-foreground py-8"
                    >
                      Bu ay için işlem bulunamadı
                    </TableCell>
                  </TableRow>
                ) : (
                  getCurrentTransactions().map((transaction) => (
                    <TableRow
                      key={transaction.id}
                      className={
                        transaction.type === "Talep" &&
                        !(
                          selectedTransactionTypes.length === 1 &&
                          selectedTransactionTypes.includes("Talep")
                        )
                          ? "bg-primary/30 hover:bg-primary/40"
                          : ""
                      }
                    >
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0">
                        {new Date(transaction.date).toLocaleDateString("tr-TR")}
                      </TableCell>
                      <TableCell className="w-20 min-w-[5rem] flex-shrink-0 font-mono text-sm">
                        {transaction.id}
                      </TableCell>
                      <TableCell className="w-20 min-w-[5rem] flex-shrink-0">
                        {transaction.type}
                      </TableCell>
                      <TableCell className="flex-1 min-w-0 max-w-30">
                        <div className="flex items-center gap-3">
                          <div
                            className="truncate"
                            title={transaction.description}
                          >
                            {transaction.description}
                          </div>
                          <div className="flex items-center gap-2 shrink-0">
                            {transaction.isNew && (
                              <Badge variant="destructive">{"Yeni"}</Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        <span
                          className={
                            transaction.amount >= 0
                              ? "text-success"
                              : "text-destructive"
                          }
                        >
                          {transaction.amount}
                        </span>
                      </TableCell>
                      <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right font-medium">
                        {transaction.newBalance}
                      </TableCell>
                      <TableCell className="w-12 min-w-[3rem] flex-shrink-0">
                        {transaction.type === "Talep" &&
                        transaction.status === "Onay bekliyor" ? (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-foreground hover:text-foreground/80">
                                <X className="w-5 h-5 text-destructive" />
                              </button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Çekim Talebini İptal Et
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  <strong className="font-mono">
                                    {transaction.id}
                                  </strong>{" "}
                                  no'lu çekim talebini iptal etmek
                                  istediğinizden emin misiniz?{" "}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Vazgeç</AlertDialogCancel>
                                <AlertDialogAction
                                  variant={"destructive"}
                                  onClick={() =>
                                    handleDeleteWithdrawal(transaction.id)
                                  }
                                >
                                  İptal Et
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        ) : transaction.type === "Talep" &&
                          transaction.status === "İşleme Alındı" ? (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-foreground hover:text-foreground/80">
                                  <Clock className="size-5" />
                                </button>
                              </TooltipTrigger>
                              <TooltipContent align="end" sideOffset={-5}>
                                <p>{transaction.status}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ) : transaction.type === "Yatırım" ||
                          transaction.type === "Çekim" ? (
                          <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-foreground hover:text-foreground/80">
                            <ReceiptText className="w-5 h-5" />
                          </button>
                        ) : null}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>

      {/* Child Dialogs */}
      <KYCDialog
        open={kycOpen}
        onOpenChange={setKycOpen}
        onSubmit={handleKycSubmit}
      />

      <BankTransferDialog
        open={bankTransferOpen}
        onOpenChange={setBankTransferOpen}
      />

      <IbanDialog
        open={ibanOpen}
        onOpenChange={setIbanOpen}
        onSubmit={handleIbanSubmit}
      />

      <WithdrawalDialog
        open={withdrawalOpen}
        onOpenChange={setWithdrawalOpen}
        balance={0} // TODO: Get actual balance from transaction data
        min={20}
        max={10000}
        onSubmit={(data) => {
          console.log("Withdrawal submitted:", data);
          setWithdrawalOpen(false);
        }}
      />

      {children}
    </Dialog>
  );
}
