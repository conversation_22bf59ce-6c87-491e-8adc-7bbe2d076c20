"use client";

import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { XIcon } from "lucide-react";

import { cn } from "@workspace/ui/lib/utils";
import { useDialogStore } from "@workspace/ui/stores/dialog-store";
import { useDialogHistory } from "@workspace/ui/hooks/use-dialog-history";

const DialogIDContext = React.createContext<string | undefined>(undefined);
const DialogCloseContext = React.createContext<(() => void) | undefined>(
  undefined,
);

function Dialog({
  open,
  onOpenChange,
  useHistory,
  historyKey,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root> & {
  useHistory?: boolean;
  historyKey?: string;
}) {
  const { addDialog, removeDialog } = useDialogStore();
  const id = React.useId();

  // Use history if explicitly requested OR if historyKey is provided
  const shouldUseHistory = useHistory === true || historyKey !== undefined;

  // Always call the hook but only use its values when needed
  const [urlOpen, setUrlOpen] = useDialogHistory(historyKey || "dialog");

  const isOpen = shouldUseHistory ? urlOpen : open;
  const setOpen = shouldUseHistory ? setUrlOpen : onOpenChange;

  React.useEffect(() => {
    if (isOpen) {
      // Extract level number from historyKey to determine order
      const levelMatch = historyKey?.match(/level(\d+)/);
      const level =
        levelMatch && levelMatch[1] ? parseInt(levelMatch[1], 10) : 0;

      // Add delay based on level to ensure correct order (level1 first, level4 last)
      const delay = level * 10; // 0ms, 10ms, 20ms, 30ms
      const timeout = setTimeout(() => {
        addDialog(id);
      }, delay);
      return () => clearTimeout(timeout);
    }
  }, [isOpen, id, addDialog, historyKey]);

  // Provide a cleanup function for child components to call
  const handleDialogClose = React.useCallback(() => {
    removeDialog(id);
  }, [removeDialog, id]);

  return (
    <DialogIDContext.Provider value={id}>
      <DialogCloseContext.Provider value={handleDialogClose}>
        <DialogPrimitive.Root
          data-slot="dialog"
          open={isOpen}
          onOpenChange={setOpen}
          {...props}
        />
      </DialogCloseContext.Provider>
    </DialogIDContext.Provider>
  );
}

function DialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
  return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />;
}

function DialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>) {
  return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

function DialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>) {
  return <DialogPrimitive.Close data-slot="dialog-close" {...props} />;
}

function DialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
  const { getIsFirstDialog } = useDialogStore();
  const id = React.useContext(DialogIDContext);

  return (
    <DialogPrimitive.Overlay
      data-slot="dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className,
        !getIsFirstDialog(id!) && "opacity-0",
      )}
      {...props}
    />
  );
}

function DialogContent({
  className,
  children,
  showCloseButton = true,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
  showCloseButton?: boolean;
}) {
  const { getIsLastDialog } = useDialogStore();
  const id = React.useContext(DialogIDContext);
  const handleDialogClose = React.useContext(DialogCloseContext);
  const isItLast = getIsLastDialog(id!);

  // Handle animation end to remove dialog from store
  const handleAnimationEnd = React.useCallback(
    (event: React.AnimationEvent) => {
      // Only handle if this is the content element and it's closing
      if (
        event.target instanceof Element &&
        event.target.getAttribute("data-slot") === "dialog-content" &&
        event.target.getAttribute("data-state") === "closed" &&
        handleDialogClose
      ) {
        handleDialogClose();
      }
    },
    [handleDialogClose],
  );

  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border-2 p-3 shadow-lg duration-200 sm:max-w-lg select-none max-h-screen overflow-y-auto overflow-x-hidden sm:overflow-visible scrollbar outline-0",
          className,
          !isItLast && "opacity-0",
        )}
        onAnimationEnd={handleAnimationEnd}
        {...props}
      >
        {showCloseButton && (
          <DialogPrimitive.Close
            data-slot="dialog-close"
            className="ring-offset-background w-min mt-2 -mb-4 ml-auto mr-2 sm:m-0 sm:absolute sm:-top-4 sm:-right-4 sm:translate-0 rounded-xs text-background border-2 inset-ring-ring inset-ring-2 drop-shadow drop-shadow-border/25 rotate-45 bg-foreground cursor-pointer z-1000"
          >
            <XIcon className="size-9 sm:size-10 p-1 -rotate-45" />
            <span className="sr-only">{"Kapat"}</span>
          </DialogPrimitive.Close>
        )}
        {children}
      </DialogPrimitive.Content>
    </DialogPortal>
  );
}

function DialogHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-header"
      className={cn("flex flex-col gap-2 text-center sm:text-left ", className)}
      {...props}
    />
  );
}

function DialogFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-footer"
      className={cn(
        "flex gap-4 flex-row justify-center mt-4 pb-2 mb-2 border-b-6 border-foreground",
        className,
      )}
      {...props}
    />
  );
}

function DialogTitle({
  className,
  variant = "default",
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title> & {
  variant?: "default" | "stripe";
}) {
  return variant === "default" ? (
    <DialogPrimitive.Title
      data-slot="dialog-title"
      className={cn(
        "relative pt-1 text-center text-lg leading-none font-semibold after:content-[''] after:border-t-6 after:border-b-2 after:border-foreground after:block after:w-full after:pt-1 after:mt-1.5",
        className,
      )}
      {...props}
    />
  ) : (
    <div className="shrink-0 flex grow relative overflow-hidden gap-1.5 h-fit -left-3">
      <DialogPrimitive.Title
        data-slot="dialog-title"
        className={cn(
          "relative overflow-hidden text-xl text-shadow-none text-background pt-1.5 pb-1 pl-6 pr-12 before:content-[''] before:bg-foreground before:inset-0 before:absolute before:-z-10 before:-translate-x-4 before:-skew-x-14",
          className,
        )}
        {...props}
      />
      <div className="-skew-x-14 flex gap-1.5 -translate-x-4">
        <div className="w-2 bg-foreground"></div>
        <div className="w-2 bg-foreground"></div>
      </div>
    </div>
  );
}

function DialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
  return (
    <DialogPrimitive.Description
      data-slot="dialog-description"
      className={cn("text-muted-foreground text-sm px-8 pt-6 pb-4", className)}
      {...props}
    />
  );
}

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
};
