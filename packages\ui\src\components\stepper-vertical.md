# Vertical Stepper Component

A comprehensive, accessible vertical stepper component built with React and TypeScript. This component provides a flexible way to create step-by-step workflows with customizable navigation and content management.

## Features

- **Vertical Layout**: Clean vertical design with connected circles and lines
- **Composable Architecture**: Multiple sub-components for different use cases
- **Flexible Navigation**: Next/Previous buttons and direct step activation
- **Content Management**: Separate components for active and inactive step content
- **Accessibility**: Full keyboard navigation and ARIA support
- **TypeScript**: Complete type safety with exported interfaces
- **Customizable Styling**: Tailwind CSS classes with variant support

## Installation

The component depends on the `headless-stepper` package for state management logic:

```bash
pnpm add headless-stepper
```

## Basic Usage

```tsx
import {
  StepperVertical,
  StepNavigation,
  StepNavigationButtons,
  StepContentActive,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "step1", label: "Personal Information" },
  { id: "step2", label: "Address Details" },
  { id: "step3", label: "Payment Method" },
  { id: "step4", label: "Review & Submit" },
];

function MyForm() {
  const [currentStep, setCurrentStep] = useState(0);

  return (
    <StepperVertical
      steps={steps}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
    >
      <div className="flex gap-8">
        {/* Navigation */}
        <StepNavigation showLabels={true} />
        
        {/* Content */}
        <div className="flex-1">
          <StepContentActive stepId="step1">
            <h3>Personal Information</h3>
            <p>Enter your personal details...</p>
          </StepContentActive>
          
          <StepContentActive stepId="step2">
            <h3>Address Details</h3>
            <p>Enter your address...</p>
          </StepContentActive>
          
          {/* Navigation Buttons */}
          <StepNavigationButtons />
        </div>
      </div>
    </StepperVertical>
  );
}
```

## Components

### StepperVertical (Main Container)

The root component that provides context to all child components.

**Props:**
- `steps: StepConfig[]` - Array of step configurations
- `currentStep?: number` - Current active step index (default: 0)
- `onStepChange?: (stepIndex: number) => void` - Callback when step changes
- `allowSkipping?: boolean` - Allow jumping to any step (default: true)
- `className?: string` - Additional CSS classes

### StepNavigation

Displays the vertical navigation with circles and connecting lines.

**Props:**
- `showLabels?: boolean` - Show step labels (default: true)
- `size?: "default" | "large"` - Circle size variant
- `className?: string` - Additional CSS classes

### StepNavigationButtons

Provides Next/Previous navigation buttons.

**Props:**
- `nextLabel?: string` - Next button text (default: "Next")
- `prevLabel?: string` - Previous button text (default: "Previous")
- `showNext?: boolean` - Show next button (default: true)
- `showPrev?: boolean` - Show previous button (default: true)
- `className?: string` - Additional CSS classes

### StepActivationButton

Button to jump directly to a specific step.

**Props:**
- `stepId: string` - ID of the step to activate
- `children: React.ReactNode` - Button content
- `className?: string` - Additional CSS classes

### StepContentActive

Shows content only when the specified step is active.

**Props:**
- `stepId: string` - ID of the step this content belongs to
- `children: React.ReactNode` - Content to show when active
- `className?: string` - Additional CSS classes

### StepContentInactive

Shows content only when the specified step is NOT active.

**Props:**
- `stepId: string` - ID of the step this content belongs to
- `children: React.ReactNode` - Content to show when inactive
- `className?: string` - Additional CSS classes

## Types

### StepConfig

```tsx
interface StepConfig {
  id: string;        // Unique identifier for the step
  label: string;     // Display label for the step
  disabled?: boolean; // Whether the step is disabled
}
```

## Styling

The component uses Tailwind CSS with the following design tokens:

- **Active Step**: `bg-foreground text-background` (dark circle with light text)
- **Inactive Step**: `bg-muted text-muted-foreground` (muted circle and text)
- **Completed Step**: `bg-foreground text-background` (same as active)
- **Connecting Lines**: `bg-foreground` for completed, `bg-muted-foreground` for incomplete

## Advanced Examples

### Controlled vs Uncontrolled

```tsx
// Controlled (external state management)
<StepperVertical
  steps={steps}
  currentStep={externalStep}
  onStepChange={setExternalStep}
>
  {/* content */}
</StepperVertical>

// Uncontrolled (internal state management)
<StepperVertical steps={steps}>
  {/* content */}
</StepperVertical>
```

### Restricted Navigation

```tsx
<StepperVertical
  steps={steps}
  allowSkipping={false} // Only allow sequential navigation
  currentStep={currentStep}
  onStepChange={setCurrentStep}
>
  {/* content */}
</StepperVertical>
```

### Custom Step Activation

```tsx
<StepperVertical steps={steps}>
  <div className="flex gap-2">
    <StepActivationButton stepId="payment">
      Skip to Payment
    </StepActivationButton>
    <StepActivationButton stepId="review">
      Skip to Review
    </StepActivationButton>
  </div>
</StepperVertical>
```

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Screen reader friendly
- Focus management
- High contrast support

## Browser Support

- Modern browsers with ES2020 support
- React 18+
- TypeScript 4.5+
