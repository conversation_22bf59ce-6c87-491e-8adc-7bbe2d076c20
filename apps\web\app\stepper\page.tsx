"use client";

import * as React from "react";
import {
  StepperVertical,
  Step,
  StepPreviousButton,
  StepNextButton,
  StepActivationButton,
  StepContentActive,
  StepContentInactive,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "kisisel", label: "Kişisel Bilgiler" },
  { id: "adres", label: "Adres Bilgileri" },
  { id: "odeme", label: "Ödeme Yöntemi" },
  { id: "onay", label: "İnceleme ve Onay" },
];

export default function StepperShowcase() {
  const [currentStep, setCurrentStep] = React.useState(0);

  return (
    <div className="container mx-auto p-8">
      <div className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Dikey Adım Bileşeni</h1>
        <p className="text-lg text-muted-foreground">
          Navigasyon ve içerik yönetimi ile dikey adım bileşeni
        </p>
      </div>

      <div className="border rounded-lg p-6 bg-card max-w-4xl mx-auto">
        <StepperVertical
          steps={steps}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
        >
          <div className="flex flex-col">
            {/* Kişisel Bilgiler */}
            <Step stepId="kisisel">
              <StepContentActive stepId="kisisel">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Kişisel Bilgiler</h3>
                  <p className="text-muted-foreground">
                    Lütfen temel kişisel bilgilerinizi girin.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Ad</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Adınızı girin"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Soyad</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Soyadınızı girin"
                      />
                    </div>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive stepId="kisisel">
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Kişisel Bilgiler Tamamlandı
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Ad, soyad ve diğer kişisel bilgiler kaydedildi
                      </p>
                    </div>
                    <StepActivationButton stepId="kisisel">
                      Düzenle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Ad: Ahmet</p>
                    <p>• Soyad: Yılmaz</p>
                    <p>• E-posta: <EMAIL></p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Adres Bilgileri */}
            <Step stepId="adres">
              <StepContentActive stepId="adres">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Adres Bilgileri</h3>
                  <p className="text-muted-foreground">
                    Adres bilgilerinizi girin.
                  </p>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Sokak Adresi
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Sokak adresinizi girin"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Şehir</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Şehrinizi girin"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Posta Kodu
                        </label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Posta kodunu girin"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive stepId="adres">
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Adres Bilgileri Tamamlandı
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Teslimat adresi ve iletişim bilgileri kaydedildi
                      </p>
                    </div>
                    <StepActivationButton stepId="adres">
                      Düzenle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Sokak: Atatürk Caddesi No:123</p>
                    <p>• Şehir: İstanbul</p>
                    <p>• Posta Kodu: 34000</p>
                    <p>• Ülke: Türkiye</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* Ödeme Yöntemi */}
            <Step stepId="odeme">
              <StepContentActive stepId="odeme">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Ödeme Yöntemi</h3>
                  <p className="text-muted-foreground">
                    Tercih ettiğiniz ödeme yöntemini seçin.
                  </p>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>Kredi Kartı</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>PayPal</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>Banka Havalesi</span>
                    </label>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive stepId="odeme">
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg border border-dashed">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        ✓ Ödeme Yöntemi Seçildi
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Güvenli ödeme yöntemi belirlendi ve doğrulandı
                      </p>
                    </div>
                    <StepActivationButton stepId="odeme">
                      Değiştir
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>• Yöntem: Kredi Kartı</p>
                    <p>• Kart: **** **** **** 1234</p>
                    <p>• Banka: Ziraat Bankası</p>
                    <p>• Güvenlik: 3D Secure aktif</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>

            {/* İnceleme ve Onay */}
            <Step stepId="onay">
              <StepContentActive stepId="onay">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">İnceleme ve Onay</h3>
                  <p className="text-muted-foreground">
                    Lütfen bilgilerinizi gözden geçirin ve onaylayın.
                  </p>
                  <div className="bg-muted p-4 rounded-md space-y-2">
                    <p>
                      <strong>Ad Soyad:</strong> Ahmet Yılmaz
                    </p>
                    <p>
                      <strong>Adres:</strong> Atatürk Cad. No:123, İstanbul,
                      34000
                    </p>
                    <p>
                      <strong>Ödeme:</strong> Kredi Kartı
                    </p>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive stepId="onay">
                <div className="space-y-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-green-800">
                        ✓ Başvuru Tamamlandı
                      </p>
                      <p className="text-xs text-green-600">
                        Tüm bilgiler onaylandı ve işlem başarıyla tamamlandı
                      </p>
                    </div>
                    <StepActivationButton stepId="onay">
                      Görüntüle
                    </StepActivationButton>
                  </div>
                  <div className="text-xs text-green-700 space-y-1">
                    <p>• Başvuru No: #2024-001234</p>
                    <p>• Tarih: {new Date().toLocaleDateString("tr-TR")}</p>
                    <p>• Durum: Onaylandı</p>
                    <p>• Tahmini İşlem Süresi: 2-3 iş günü</p>
                  </div>
                </div>
              </StepContentInactive>
            </Step>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            <StepPreviousButton />
            <StepNextButton />
          </div>
        </StepperVertical>
      </div>
    </div>
  );
}
