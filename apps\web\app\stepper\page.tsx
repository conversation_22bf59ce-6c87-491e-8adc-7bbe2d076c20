"use client";

import * as React from "react";
import {
  StepperVertical,
  StepNavigation,
  StepNavigationButtons,
  StepContentActive,
  type StepConfig,
  StepContentInactive,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "personal", label: "Personal Information" },
  { id: "address", label: "Address Details" },
  { id: "payment", label: "Payment Method" },
  { id: "review", label: "Review & Submit" },
];

export default function StepperShowcase() {
  const [currentStep, setCurrentStep] = React.useState(0);

  return (
    <div className="container mx-auto p-8">
      <div className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Vertical Stepper Component</h1>
        <p className="text-lg text-muted-foreground">
          A vertical stepper with navigation and content management
        </p>
      </div>

      <div className="border rounded-lg p-6 bg-card max-w-4xl mx-auto">
        <StepperVertical
          steps={steps}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
        >
          <div className="flex gap-8">
            {/* Navigation */}
            <div className="w-64">
              <StepNavigation showLabels={false} size="default" />
            </div>

            {/* Content Area */}
            <div className="flex-1 space-y-6">
              <StepContentActive stepId="personal">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">
                    Personal Information
                  </h3>
                  <p className="text-muted-foreground">
                    Please provide your basic personal details.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">First Name</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Enter your first name"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Last Name</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Enter your last name"
                      />
                    </div>
                  </div>
                </div>
              </StepContentActive>
              <StepContentInactive stepId="personal">
                <div className="space-y-4">
                  <p className="text-muted-foreground">Done</p>
                </div>
              </StepContentInactive>

              <StepContentActive stepId="address">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Address Details</h3>
                  <p className="text-muted-foreground">
                    Enter your address information.
                  </p>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Street Address
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Enter your street address"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">City</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Enter your city"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Postal Code
                        </label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Enter postal code"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </StepContentActive>

              <StepContentActive stepId="payment">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Payment Method</h3>
                  <p className="text-muted-foreground">
                    Choose your preferred payment method.
                  </p>
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>Credit Card</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>PayPal</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input type="radio" name="payment" className="w-4 h-4" />
                      <span>Bank Transfer</span>
                    </label>
                  </div>
                </div>
              </StepContentActive>

              <StepContentActive stepId="review">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">Review & Submit</h3>
                  <p className="text-muted-foreground">
                    Please review your information before submitting.
                  </p>
                  <div className="bg-muted p-4 rounded-md space-y-2">
                    <p>
                      <strong>Name:</strong> John Doe
                    </p>
                    <p>
                      <strong>Address:</strong> 123 Main St, City, 12345
                    </p>
                    <p>
                      <strong>Payment:</strong> Credit Card
                    </p>
                  </div>
                </div>
              </StepContentActive>

              {/* Navigation Buttons */}
              <StepNavigationButtons
                className="pt-4"
                nextLabel="Continue"
                prevLabel="Back"
              />
            </div>
          </div>
        </StepperVertical>
      </div>
    </div>
  );
}
