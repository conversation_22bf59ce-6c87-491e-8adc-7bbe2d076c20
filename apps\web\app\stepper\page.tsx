"use client";

import * as React from "react";
import {
  StepperVertical,
  StepNavigation,
  StepPreviousButton,
  StepNextButton,
  StepContentActive,
  StepContentInactive,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "kisisel", label: "Kişisel Bilgiler" },
  { id: "adres", label: "Adres Bilgileri" },
  { id: "odeme", label: "Ödeme Yöntemi" },
  { id: "onay", label: "İnceleme ve Onay" },
];

export default function StepperShowcase() {
  const [currentStep, setCurrentStep] = React.useState(0);

  return (
    <div className="container mx-auto p-8">
      <div className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold">Dikey Adım Bileşeni</h1>
        <p className="text-lg text-muted-foreground">
          Navigasyon ve içerik yönetimi ile dikey adım bileşeni
        </p>
      </div>

      <div className="border rounded-lg p-6 bg-card max-w-4xl mx-auto">
        <StepperVertical
          steps={steps}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
        >
          <StepNavigation size="default">
            {/* Kişisel Bilgiler */}
            <StepContentActive stepId="kisisel">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Kişisel Bilgiler</h3>
                <p className="text-muted-foreground">
                  Lütfen temel kişisel bilgilerinizi girin.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Ad</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md"
                      placeholder="Adınızı girin"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Soyad</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md"
                      placeholder="Soyadınızı girin"
                    />
                  </div>
                </div>
              </div>
            </StepContentActive>
            <StepContentInactive stepId="kisisel">
              <div className="space-y-2 flex items-center">
                <p className="text-muted-foreground text-sm">
                  ✓ Kişisel bilgiler tamamlandı
                </p>
              </div>
            </StepContentInactive>

            {/* Adres Bilgileri */}
            <StepContentActive stepId="adres">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Adres Bilgileri</h3>
                <p className="text-muted-foreground">
                  Adres bilgilerinizi girin.
                </p>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sokak Adresi</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md"
                      placeholder="Sokak adresinizi girin"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Şehir</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Şehrinizi girin"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Posta Kodu</label>
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md"
                        placeholder="Posta kodunu girin"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </StepContentActive>
            <StepContentInactive stepId="adres">
              <div className="space-y-2 flex items-center">
                <p className="text-muted-foreground text-sm">
                  ✓ Adres bilgileri tamamlandı
                </p>
              </div>
            </StepContentInactive>

            {/* Ödeme Yöntemi */}
            <StepContentActive stepId="odeme">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Ödeme Yöntemi</h3>
                <p className="text-muted-foreground">
                  Tercih ettiğiniz ödeme yöntemini seçin.
                </p>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="radio" name="payment" className="w-4 h-4" />
                    <span>Kredi Kartı</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input type="radio" name="payment" className="w-4 h-4" />
                    <span>PayPal</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input type="radio" name="payment" className="w-4 h-4" />
                    <span>Banka Havalesi</span>
                  </label>
                </div>
              </div>
            </StepContentActive>
            <StepContentInactive stepId="odeme">
              <div className="space-y-2 flex items-center">
                <p className="text-muted-foreground text-sm">
                  ✓ Ödeme yöntemi seçildi
                </p>
              </div>
            </StepContentInactive>

            {/* İnceleme ve Onay */}
            <StepContentActive stepId="onay">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">İnceleme ve Onay</h3>
                <p className="text-muted-foreground">
                  Lütfen bilgilerinizi gözden geçirin ve onaylayın.
                </p>
                <div className="bg-muted p-4 rounded-md space-y-2">
                  <p>
                    <strong>Ad Soyad:</strong> Ahmet Yılmaz
                  </p>
                  <p>
                    <strong>Adres:</strong> Atatürk Cad. No:123, İstanbul, 34000
                  </p>
                  <p>
                    <strong>Ödeme:</strong> Kredi Kartı
                  </p>
                </div>
              </div>
            </StepContentActive>
            <StepContentInactive stepId="onay">
              <div className="space-y-2 flex items-center">
                <p className="text-muted-foreground text-sm">
                  ✓ Onay tamamlandı
                </p>
              </div>
            </StepContentInactive>
          </StepNavigation>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            <StepPreviousButton />
            <StepNextButton />
          </div>
        </StepperVertical>
      </div>
    </div>
  );
}
