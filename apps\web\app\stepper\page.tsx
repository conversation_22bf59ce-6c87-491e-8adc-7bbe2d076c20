"use client";

import * as React from "react";
import {
  StepperVertical,
  StepNavigation,
  StepNavigationButtons,
  StepActivationButton,
  StepContentActive,
  StepContentInactive,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";

const steps: StepConfig[] = [
  { id: "personal", label: "Personal Information" },
  { id: "address", label: "Address Details" },
  { id: "payment", label: "Payment Method" },
  { id: "review", label: "Review & Submit" },
  { id: "confirmation", label: "Confirmation", disabled: true },
];

export default function StepperShowcase() {
  const [currentStep, setCurrentStep] = React.useState(0);

  return (
    <div className="container mx-auto p-8 space-y-12">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Vertical Stepper Component</h1>
        <p className="text-lg text-muted-foreground">
          A comprehensive stepper component with navigation, content management, and customizable styling
        </p>
      </div>

      {/* Basic Stepper Example */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Basic Stepper with Navigation</h2>
        <div className="border rounded-lg p-6 bg-card">
          <StepperVertical
            steps={steps}
            currentStep={currentStep}
            onStepChange={setCurrentStep}
            className="max-w-2xl"
          >
            <div className="flex gap-8">
              {/* Navigation */}
              <div className="w-64">
                <StepNavigation showLabels={true} size="default" />
              </div>

              {/* Content Area */}
              <div className="flex-1 space-y-6">
                <StepContentActive stepId="personal">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Personal Information</h3>
                    <p className="text-muted-foreground">
                      Please provide your basic personal details to get started.
                    </p>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">First Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Last Name</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>
                  </div>
                </StepContentActive>

                <StepContentActive stepId="address">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Address Details</h3>
                    <p className="text-muted-foreground">
                      We need your address information for delivery purposes.
                    </p>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Street Address</label>
                        <input
                          type="text"
                          className="w-full p-2 border rounded-md"
                          placeholder="Enter your street address"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">City</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded-md"
                            placeholder="Enter your city"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Postal Code</label>
                          <input
                            type="text"
                            className="w-full p-2 border rounded-md"
                            placeholder="Enter postal code"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </StepContentActive>

                <StepContentActive stepId="payment">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Payment Method</h3>
                    <p className="text-muted-foreground">
                      Choose your preferred payment method.
                    </p>
                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input type="radio" name="payment" className="w-4 h-4" />
                        <span>Credit Card</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="radio" name="payment" className="w-4 h-4" />
                        <span>PayPal</span>
                      </label>
                      <label className="flex items-center space-x-3">
                        <input type="radio" name="payment" className="w-4 h-4" />
                        <span>Bank Transfer</span>
                      </label>
                    </div>
                  </div>
                </StepContentActive>

                <StepContentActive stepId="review">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Review & Submit</h3>
                    <p className="text-muted-foreground">
                      Please review your information before submitting.
                    </p>
                    <div className="bg-muted p-4 rounded-md space-y-2">
                      <p><strong>Name:</strong> John Doe</p>
                      <p><strong>Address:</strong> 123 Main St, City, 12345</p>
                      <p><strong>Payment:</strong> Credit Card</p>
                    </div>
                  </div>
                </StepContentActive>

                <StepContentActive stepId="confirmation">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold">Confirmation</h3>
                    <p className="text-muted-foreground">
                      Thank you! Your submission has been received.
                    </p>
                    <div className="bg-green-50 border border-green-200 p-4 rounded-md">
                      <p className="text-green-800">✓ Your order has been successfully submitted!</p>
                    </div>
                  </div>
                </StepContentActive>

                {/* Navigation Buttons */}
                <StepNavigationButtons
                  className="pt-4"
                  nextLabel="Continue"
                  prevLabel="Back"
                />
              </div>
            </div>
          </StepperVertical>
        </div>
      </section>

      {/* Compact Stepper Example */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Compact Stepper (Numbers Only)</h2>
        <div className="border rounded-lg p-6 bg-card">
          <StepperVertical
            steps={steps.slice(0, 4)} // Exclude disabled step for this example
            currentStep={1}
            className="max-w-md"
          >
            <div className="flex gap-6">
              <StepNavigation showLabels={false} size="default" />
              <div className="flex-1">
                <p className="text-muted-foreground">
                  This is a compact version showing only step numbers without labels.
                  Current step: <strong>{steps[1].label}</strong>
                </p>
              </div>
            </div>
          </StepperVertical>
        </div>
      </section>

      {/* Step Activation Buttons Example */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Step Activation Buttons</h2>
        <div className="border rounded-lg p-6 bg-card">
          <StepperVertical
            steps={steps.slice(0, 4)}
            currentStep={0}
            className="space-y-4"
          >
            <p className="text-muted-foreground mb-4">
              Use these buttons to jump directly to specific steps:
            </p>
            <div className="flex gap-2 flex-wrap">
              <StepActivationButton stepId="personal">
                Go to Personal Info
              </StepActivationButton>
              <StepActivationButton stepId="address">
                Go to Address
              </StepActivationButton>
              <StepActivationButton stepId="payment">
                Go to Payment
              </StepActivationButton>
              <StepActivationButton stepId="review">
                Go to Review
              </StepActivationButton>
            </div>
          </StepperVertical>
        </div>
      </section>

      {/* Inactive Content Example */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Active vs Inactive Content</h2>
        <div className="border rounded-lg p-6 bg-card">
          <StepperVertical
            steps={steps.slice(0, 3)}
            currentStep={1}
            className="space-y-4"
          >
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Step 1 (Inactive)</h4>
                <StepContentInactive stepId="personal">
                  <div className="p-3 border rounded">
                    <p className="text-sm">Personal information form (completed)</p>
                  </div>
                </StepContentInactive>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Step 2 (Active)</h4>
                <StepContentActive stepId="address">
                  <div className="p-3 border-2 border-primary rounded">
                    <p className="text-sm">Address details form (current)</p>
                  </div>
                </StepContentActive>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Step 3 (Inactive)</h4>
                <StepContentInactive stepId="payment">
                  <div className="p-3 border rounded">
                    <p className="text-sm">Payment method form (upcoming)</p>
                  </div>
                </StepContentInactive>
              </div>
            </div>
          </StepperVertical>
        </div>
      </section>
    </div>
  );
}
