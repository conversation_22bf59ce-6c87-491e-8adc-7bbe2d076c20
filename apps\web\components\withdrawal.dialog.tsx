"use client";

import * as React from "react";
import { useState } from "react";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { z } from "zod";
import { Minus, Plus, Info } from "lucide-react";
import Image from "next/image";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  ButtonGroup,
  ButtonGroupItem,
} from "@workspace/ui/components/button-group";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { InputValidated } from "@workspace/ui/components/input-validated";
import { Label } from "@workspace/ui/components/label";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { FeedbackDialog } from "./feedback.dialog";

// Constants
const SODA_TO_TL_RATE = 10; // 1 SODA = 10 TL
const COMMISSION_RATE = 0.4; // 40%

// Turkish locale number formatting function
const formatTurkishCurrency = (amount: number): string => {
  return new Intl.NumberFormat("tr-TR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Dynamic Zod schema factory for withdrawal form validation
const createWithdrawalSchema = (min: number, max: number) =>
  z.object({
    amount: z
      .number()
      .min(min, `Miktar en az ${min} olmalıdır`)
      .max(max, `Miktar en fazla ${max} olabilir`),
  });

export interface WithdrawalDialogProps {
  /** Whether the withdrawal dialog is open */
  open?: boolean;
  /** Callback when dialog open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Current user balance (bakiye) */
  balance?: number;
  /** Minimum withdrawal amount */
  min?: number;
  /** Maximum withdrawal amount (usually the balance) */
  max?: number;
  /** Callback when withdrawal is submitted */
  onSubmit?: (data: WithdrawalFormData) => void;
  /** Whether to show the close button (X) in the dialog */
  showCloseButton?: boolean;
}

export type WithdrawalFormData = {
  amount: number;
};

export function WithdrawalDialog({
  open = false,
  onOpenChange,
  balance = 1250,
  min = 1,
  max,
  onSubmit,
  showCloseButton = true,
}: WithdrawalDialogProps) {
  // Use max as balance if not provided, but ensure it doesn't exceed balance
  const effectiveMax = Math.min(max ?? balance, balance);
  const effectiveMin = Math.max(min, 1); // Ensure minimum is at least 1

  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create dynamic schema based on min/max values
  const withdrawalSchema = React.useMemo(
    () => createWithdrawalSchema(effectiveMin, effectiveMax),
    [effectiveMin, effectiveMax],
  );

  const form = useForm({
    defaultValues: {
      amount: Math.max(effectiveMin, Math.min(20, effectiveMax)),
    },
    validationLogic: revalidateLogic({
      mode: "submit",
      modeAfterSubmission: "change",
    }),
    validators: {
      onDynamic: withdrawalSchema,
    },
    onSubmit: async ({ value }) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500));
        onSubmit?.(value);
        setShowFeedbackDialog(true);
      } catch (error) {
        console.error("Withdrawal failed:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  const handleAmountChange = (newAmount: number) => {
    const clampedAmount = Math.max(
      effectiveMin,
      Math.min(newAmount, effectiveMax),
    );
    form.setFieldValue("amount", clampedAmount);
  };

  const handleBalanceClick = () => {
    const clampedBalance = Math.max(
      effectiveMin,
      Math.min(balance, effectiveMax),
    );
    form.setFieldValue("amount", clampedBalance);
  };

  // Use form state to get current amount and ensure reactivity
  const [currentAmount, setCurrentAmount] = useState(0);

  // Subscribe to form changes
  React.useEffect(() => {
    const unsubscribe = form.store.subscribe(() => {
      const newAmount = form.getFieldValue("amount");
      setCurrentAmount(newAmount);
    });

    // Set initial value
    setCurrentAmount(form.getFieldValue("amount"));

    return unsubscribe;
  }, [form]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <DialogContent
            showCloseButton={showCloseButton}
            onEscapeKeyDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onInteractOutside={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            className="sm:max-w-[560px]"
          >
            <DialogHeader>
              <DialogTitle>{"Çekim Talebi"}</DialogTitle>
            </DialogHeader>

            {/* Balance display */}
            <div className="text-center text-lg text-muted-foreground my-4">
              {"Bakiye: "}
              <button
                type="button"
                onClick={handleBalanceClick}
                disabled={isSubmitting}
                className="text-foreground hover:text-foreground transition-colors underline decoration-4 decoration-foreground/50 decoration-dotted underline-offset-6"
              >
                {balance + " SODA"}
              </button>
            </div>
            <div className="space-y-6 mx-6">
              {/* Main input section with soda image and amount controls */}
              <div className="space-y-2">
                <Label htmlFor="amount" className="ml-1 gap-1">
                  {"SODA Miktarı"}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="w-4 h-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Min: {min} SODA, Maks: {max} SODA
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </Label>
                <div className="py-6 bg-muted rounded-lg">
                  <div className="flex items-center mx-auto px-4 gap-5 max-w-sm">
                    {/* Plus/Minus buttons */}
                    <div className="flex-shrink-0">
                      <ButtonGroup>
                        <ButtonGroupItem
                          type="button"
                          disabled={
                            currentAmount <= effectiveMin || isSubmitting
                          }
                          onClick={() => handleAmountChange(currentAmount - 1)}
                        >
                          <Minus className="w-4 h-4" />
                        </ButtonGroupItem>
                        <ButtonGroupItem
                          type="button"
                          disabled={
                            currentAmount >= effectiveMax || isSubmitting
                          }
                          onClick={() => handleAmountChange(currentAmount + 1)}
                        >
                          <Plus className="w-4 h-4" />
                        </ButtonGroupItem>
                      </ButtonGroup>
                    </div>
                    <div className="flex-1">
                      <form.Field name="amount">
                        {(field) => (
                          <div className="relative">
                            <InputValidated
                              autoFocus
                              id={field.name}
                              name={field.name}
                              type="number"
                              pattern="\d+"
                              min={effectiveMin}
                              max={effectiveMax}
                              step={1}
                              disabled={isSubmitting}
                              value={field.state.value.toString()}
                              onChange={(e) => {
                                const value = parseFloat(e.target.value) || 0;
                                // Allow free typing without clamping during onChange
                                field.handleChange(value);
                              }}
                              onBlur={(e) => {
                                // Ensure value is within bounds on blur
                                const value =
                                  parseFloat(e.target.value) || effectiveMin;
                                const clampedValue = Math.max(
                                  effectiveMin,
                                  Math.min(value, effectiveMax),
                                );
                                if (clampedValue !== value) {
                                  field.handleChange(clampedValue);
                                }
                                field.handleBlur();
                              }}
                              status={
                                field.state.meta.isTouched &&
                                field.state.meta.errors.length > 0
                                  ? "error"
                                  : undefined
                              }
                              message={
                                field.state.meta.isTouched &&
                                field.state.meta.errors.length > 0
                                  ? String(field.state.meta.errors[0]?.message)
                                  : undefined
                              }
                              className="text-right md:text-lg leading-0 pr-13 rounded-full border-2 pl-6 py-1.5 font-mono font-bold"
                            />
                            {/* Soda image positioned inside the input */}
                            <div className="absolute right-1 top-1/2 transform -translate-y-3/5">
                              <div className="w-11 h-11 relative">
                                <Image
                                  src="/assets/soda-sm.png"
                                  alt="Soda"
                                  fill
                                  className="object-contain"
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </form.Field>
                    </div>
                  </div>
                </div>
                {/* Three column grid layout for Brüt, Komisyon, Net */}
                <div className="grid grid-cols-3 gap-4 text-base my-5">
                  {/* Brüt Column */}
                  <div className="text-center space-y-2">
                    <div className="flex items-center justify-center gap-1">
                      <span>{"Brüt"}</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="w-4 h-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>1 SODA = {SODA_TO_TL_RATE} TL</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <div className="text-muted-foreground">
                      {formatTurkishCurrency(currentAmount * SODA_TO_TL_RATE)}{" "}
                      TL
                    </div>
                  </div>

                  {/* Komisyon Column */}
                  <div className="text-center space-y-2">
                    <div className="flex items-center justify-center gap-1">
                      <span>{"Komisyon"}</span>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="w-4 h-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Oran: %{(COMMISSION_RATE * 100).toFixed(0)}</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <div className="text-muted-foreground">
                      {formatTurkishCurrency(
                        currentAmount * SODA_TO_TL_RATE * COMMISSION_RATE,
                      )}{" "}
                      TL
                    </div>
                  </div>

                  {/* Net Column */}
                  <div className="text-center space-y-2">
                    <div>{"Net"}</div>
                    <div className="text-muted-foreground">
                      {formatTurkishCurrency(
                        currentAmount * SODA_TO_TL_RATE * (1 - COMMISSION_RATE),
                      )}{" "}
                      TL
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="primary"
                disabled={isSubmitting || currentAmount <= 0}
                processing={isSubmitting}
                onClick={async () => {
                  setIsSubmitting(true);
                  try {
                    // Simulate API call
                    await new Promise((resolve) => setTimeout(resolve, 1500));
                    onSubmit?.({ amount: currentAmount });
                    setShowFeedbackDialog(true);
                  } catch (error) {
                    console.error("Withdrawal failed:", error);
                  } finally {
                    setIsSubmitting(false);
                  }
                }}
              >
                {"ONAYLA"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>

        {/* Feedback Dialog */}
        <FeedbackDialog
          open={showFeedbackDialog}
          onOpenChange={setShowFeedbackDialog}
          message="Çekim Talebi Alındı"
          content={[
            `Brüt Tutar: ${formatTurkishCurrency(currentAmount * SODA_TO_TL_RATE)} TL`,
            `Komisyon (%${(COMMISSION_RATE * 100).toFixed(0)}): ${formatTurkishCurrency(currentAmount * SODA_TO_TL_RATE * COMMISSION_RATE)} TL`,
            `Net Tutar: ${formatTurkishCurrency(currentAmount * SODA_TO_TL_RATE * (1 - COMMISSION_RATE))} TL`,
            "Çekim talepleri bir sonraki ayın ilk iş günü değerlendirilir.",
          ]}
          onButtonClick={() => {
            onOpenChange?.(false);
          }}
          showCloseButton={false}
        />
      </Dialog>
    </>
  );
}
