import Link from "next/link";

const componentRoutes = [
  {
    category: "Buttons",
    routes: [
      { name: "Default <PERSON><PERSON>", path: "/buttons/default" },
      { name: "Primary Button", path: "/buttons/primary" },
      { name: "<PERSON>ton Sizes", path: "/buttons/sizes" },
      { name: "Button Group", path: "/buttons/group" },
      { name: "Toggle Group", path: "/buttons/toggle-group" },
      { name: "Disabled <PERSON><PERSON>", path: "/buttons/disabled" },
      { name: "<PERSON><PERSON><PERSON>", path: "/buttons/error-button" },
      { name: "Default and Primary", path: "/buttons/default-and-primary" },
      { name: "Approval on Open", path: "/buttons/approval-onopen" },
    ],
  },
  {
    category: "Navigation",
    routes: [
      { name: "Vertical Stepper", path: "/stepper" },
    ],
  },
  {
    category: "Tabs",
    routes: [
      { name: "Two Tabs", path: "/tabs/two-tabs" },
      { name: "Four Tabs", path: "/tabs/four-tabs" },
    ],
  },
  {
    category: "Inputs",
    routes: [
      { name: "Checkbox", path: "/inputs/checkbox" },
      { name: "Nested Checkboxes", path: "/inputs/nested-checkboxes" },
      { name: "Validated Input", path: "/inputs/validated-input" },
    ],
  },
  {
    category: "Dialogs",
    routes: [
      { name: "Activity Dialog", path: "/dialogs/activity" },
      { name: "Bank Transfer Dialog", path: "/dialogs/bank-transfer" },
      { name: "IBAN Dialog", path: "/dialogs/iban" },
      { name: "Info Dialog", path: "/dialogs/info" },
      { name: "KYC Dialog", path: "/dialogs/kyc" },
      { name: "Nested Dialog", path: "/dialogs/nested" },
      { name: "Order Dialog", path: "/dialogs/order" },
      { name: "Profile Dialog", path: "/dialogs/profile" },
      { name: "Wallet Dialog", path: "/dialogs/wallet" },
      { name: "Withdrawal Dialog", path: "/dialogs/withdrawal" },
    ],
  },
  {
    category: "Popovers",
    routes: [
      { name: "Approval Popover", path: "/popovers/approval" },
    ],
  },
  {
    category: "Hover",
    routes: [
      { name: "Tooltips", path: "/hover/tooltips" },
    ],
  },
];

export default function HomePage() {
  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <div className="text-center space-y-4 mb-12">
        <h1 className="text-4xl font-bold">UI Component Showcase</h1>
        <p className="text-lg text-muted-foreground">
          Explore all available UI components and their variations
        </p>
      </div>

      <div className="grid gap-8">
        {componentRoutes.map((category) => (
          <section key={category.category} className="space-y-4">
            <h2 className="text-2xl font-semibold border-b pb-2">
              {category.category}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {category.routes.map((route) => (
                <Link
                  key={route.path}
                  href={route.path}
                  className="block p-4 border rounded-lg hover:bg-muted transition-colors"
                >
                  <span className="font-medium">{route.name}</span>
                </Link>
              ))}
            </div>
          </section>
        ))}
      </div>

      <div className="mt-12 text-center">
        <p className="text-sm text-muted-foreground">
          Click on any component to see its implementation and variations
        </p>
      </div>
    </div>
  );
}
