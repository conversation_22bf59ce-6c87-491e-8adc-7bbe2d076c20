{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "prebuild": "node ./scripts/generate-folder-manifest.mjs", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@tanstack/react-form": "^1.19.3", "@tanstack/react-query": "^5.85.5", "@workspace/ui": "workspace:*", "lucide-react": "^0.544.0", "next": "^15.4.5", "next-themes": "^0.4.6", "nuqs": "^2.6.0", "react": "^19.1.1", "react-dom": "^19.1.1", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.9.2"}}