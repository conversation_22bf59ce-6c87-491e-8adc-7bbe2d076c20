"use client";

import * as React from "react";
import { Search as SearchIcon, X } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { cn } from "@workspace/ui/lib/utils";

export interface SearchProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  /**
   * Value of the search input
   */
  value?: string;
  /**
   * <PERSON><PERSON> fired when the search value changes
   */
  onValueChange?: (value: string) => void;
  /**
   * <PERSON><PERSON> fired when the search is submitted (Enter key or search button)
   */
  onSearch?: (value: string) => void;
  /**
   * Whether the search popover is open
   */
  open?: boolean;
  /**
   * Callback fired when the open state changes
   */
  onOpenChange?: (open: boolean) => void;
  /**
   * Size of the search button
   */
  size?: "default" | "icon" | "small";
  /**
   * Alignment of the popover content
   */
  align?: "start" | "center" | "end";
  side?: "top" | "right" | "bottom" | "left";
  /**
   * Additional className for the popover content
   */
  className?: string;
  /**
   * Additional className for the search input
   */
  inputClassName?: string;
  /**
   * Additional className for the search button
   */
  buttonClassName?: string;
  /**
   * Whether the search input should auto-focus when opened
   */
  autoFocus?: boolean;
  /**
   * Whether to show the search button
   */
  showButton?: boolean;
}

export const Search = React.forwardRef<HTMLInputElement, SearchProps>(
  (
    {
      placeholder = "Ara",
      value,
      onValueChange,
      onSearch,
      open,
      onOpenChange,
      size = "icon",
      side = "bottom",
      align = "end",
      className = "w-44",
      inputClassName,
      buttonClassName,
      autoFocus = true,
      showButton = true,
      ...props
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = React.useState(value || "");
    const [internalOpen, setInternalOpen] = React.useState(false);
    const inputRef = React.useRef<HTMLInputElement>(null);

    const isControlled = value !== undefined;
    const currentValue = isControlled ? value : internalValue;
    const currentOpen = open !== undefined ? open : internalOpen;

    const handleValueChange = (newValue: string) => {
      if (!isControlled) {
        setInternalValue(newValue);
      }
      onValueChange?.(newValue);
    };

    const handleOpenChange = (newOpen: boolean) => {
      if (open === undefined) {
        setInternalOpen(newOpen);
      }
      onOpenChange?.(newOpen);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        e.preventDefault();
        onSearch?.(currentValue);
        handleOpenChange(false);
      }
    };

    if (!showButton) {
      return (
        <Input
          ref={ref || inputRef}
          placeholder={placeholder}
          value={currentValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleValueChange(e.target.value)
          }
          onKeyDown={handleKeyDown}
          className={cn(
            "outline-none border-2 border-border rounded-lg",
            inputClassName,
          )}
          {...props}
        />
      );
    }

    return (
      <Popover open={currentOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            size={size}
            className={cn(buttonClassName)}
            variant={currentValue && !currentOpen ? "primary" : "default"}
          >
            <SearchIcon
              className={cn(
                "size-6 stroke-2",
                currentValue && !currentOpen && "fill-background",
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align={align}
          side={side}
          className={cn(
            className,
            "p-0 rounded-lg border-2 overflow-clip pointer-events-auto",
          )}
        >
          <Input
            ref={ref || inputRef}
            placeholder={placeholder}
            value={currentValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleValueChange(e.target.value)
            }
            onKeyDown={handleKeyDown}
            className={cn("outline-none border-0 pr-8", inputClassName)}
            autoFocus={autoFocus}
            {...props}
          />
          {currentValue && (
            <button
              id="clean-search"
              className="absolute right-2 top-2"
              onClick={() => {
                handleValueChange("");
                // Focus back to the input after clearing
                const inputElement = ref || inputRef;
                if (
                  inputElement &&
                  "current" in inputElement &&
                  inputElement.current
                ) {
                  inputElement.current.focus();
                }
              }}
            >
              <X className="size-6 stroke-2" />
            </button>
          )}
        </PopoverContent>
      </Popover>
    );
  },
);

Search.displayName = "Search";
