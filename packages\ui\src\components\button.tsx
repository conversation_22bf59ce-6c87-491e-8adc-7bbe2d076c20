"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";
import BouncingCircles from "@workspace/ui/components/icons/bouncing-circles";
import FadeStaggerCircles from "@workspace/ui/components/icons/fade-stagger-circles";
import { TooltipNotification } from "@workspace/ui/components/tooltip-notification";

const buttonVariants = cva(
  "relative inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-xl font-bold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-6 shrink-0 [&_svg]:shrink-0 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-border fake-text-stroke border-2 inset-ring-4 focus-visible:outline-3 select-none uppercase",
  {
    variants: {
      variant: {
        default: "bg-background inset-ring-ring",
        destructive:
          "bg-destructive text-background fake-text-stroke-black inset-ring-ring/15",
        primary: "bg-primary inset-ring-ring/30",
        secondary:
          "bg-foreground/80 text-background text-shadow-none inset-ring-ring/15",
      },
      size: {
        default: "h-14 rounded-md px-6 pt-0.5 drop-shadow-lg/30",
        medium: "h-12 rounded-md text-base px-6 pt-0.5 drop-shadow-md/30",
        small:
          "h-10 rounded-lg px-4 pt-0.5 drop-shadow-sm/30 text-sm inset-ring-3",
        icon: "size-10 shrink-0 rounded-lg drop-shadow-sm/30 text-sm inset-ring-3",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

type ButtonStateProps = {
  pending?: boolean;
  processing?: boolean;
  errorMessage?: string;
  errorMessageTimer?: number;
  alignOffset?: number;
};

function Button({
  className,
  variant,
  size,
  asChild = false,
  pending,
  processing,
  disabled,
  children,
  type = "button",
  errorMessage,
  errorMessageTimer = 10000,
  alignOffset = -60,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> &
  ButtonStateProps & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";
  const isBusy = Boolean(pending || processing);
  const indicatorSize = size === "icon" ? 16 : 20;

  // State for tooltip visibility with timer
  const [showTooltip, setShowTooltip] = React.useState(
    errorMessage ? true : false,
  );

  // Use ref to store timer ID for proper cleanup
  const timerRef = React.useRef<NodeJS.Timeout | null>(null);

  // Clear any existing timer
  const clearTimer = React.useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Start a new timer
  const startTimer = React.useCallback(() => {
    if (errorMessageTimer > 0) {
      clearTimer(); // Clear any existing timer first
      timerRef.current = setTimeout(() => {
        setShowTooltip(false);
        timerRef.current = null;
      }, errorMessageTimer);
    }
  }, [errorMessageTimer, clearTimer]);

  React.useEffect(() => {
    if (errorMessageTimer === 0) return;

    if (errorMessage) {
      setShowTooltip(true);
      startTimer();
    } else {
      setShowTooltip(false);
      clearTimer();
    }

    // Cleanup on unmount or dependency change
    return clearTimer;
  }, [errorMessage, errorMessageTimer, startTimer, clearTimer]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // If there's an error message, show tooltip again and reset timer
    if (errorMessage) {
      setShowTooltip(true);
      startTimer(); // This will clear existing timer and start a new one
    }

    // Call original onClick if provided
    props.onClick?.(e);
  };

  const buttonContent = (
    <>
      {/* Overlay icon when busy */}
      {isBusy &&
        (pending ? (
          <BouncingCircles
            size={indicatorSize}
            className="absolute fill-current stroke-current size-10"
          />
        ) : (
          <FadeStaggerCircles
            size={indicatorSize}
            className="absolute fill-current stroke-current size-11"
          />
        ))}
      {/* Keep original width by hiding text visually when busy */}
      <span className={cn("flex gap-2 items-center", isBusy && "invisible")}>
        {children}
      </span>
    </>
  );

  if (errorMessage) {
    return (
      <Comp
        data-slot="button"
        className={cn(buttonVariants({ variant, size, className }))}
        disabled={disabled || isBusy}
        type={type}
        {...props}
        onClick={handleClick}
      >
        <TooltipNotification
          open={showTooltip}
          message={errorMessage}
          tooltipContentProps={{ alignOffset, align: "end" }}
        >
          <span className="absolute top-0 right-7"></span>
        </TooltipNotification>
        {buttonContent}
      </Comp>
    );
  }

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      disabled={disabled || isBusy}
      type={type}
      {...props}
    >
      {buttonContent}
    </Comp>
  );
}

export { Button, buttonVariants };
