"use client";

import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@workspace/ui/lib/utils";

// Generic segmented control styles exported for reuse (e.g., ButtonGroup)
export const segmentedContainerClasses =
  "bg-background text-foreground flex w-fit items-stretch justify-center border-2 rounded-full overflow-clip divide-x-2 divide-muted-foreground drop-shadow-sm text-sm";

export const segmentedItemBaseClasses =
  "select-none min-w-20 flex items-center justify-center gap-1.5 first:pl-8 last:pr-8 px-7 py-2 text-foreground whitespace-nowrap transition-[color,box-shadow] disabled:pointer-events-none disabled:to-muted disabled:text-muted-foreground [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-5 [&_svg:not([class*='stroke-'])]:stroke-3 -skew-x-12 from-muted-foreground/50 to-background to-10% data-[state=active]:bg-primary data-[state=inactive]:bg-gradient-to-t data-[state=inactive]:inset-shadow-xs data-[state=inactive]:inset-shadow-ring data-[state=active]:inset-shadow-sm data-[state=active]:inset-shadow-orange-800/50 [&>*]:skew-x-12 [&>*]:fake-text-stroke-muted data-[state=active]:[&>*]:translate-y-px";

export const segmentedItemStateClasses =
  "data-[state=active]:bg-primary data-[state=active]:cursor-auto data-[state=active]:inset-shadow-sm data-[state=active]:inset-shadow-orange-800/50 data-[state=active]:[&>*]:translate-y-px data-[state=inactive]:bg-gradient-to-t data-[state=inactive]:inset-shadow-xs data-[state=inactive]:inset-shadow-ring";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-2", className)}
      {...props}
    />
  );
}

function TabsList({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  return (
    <TabsPrimitive.List
      data-slot="tabs-list"
      className={cn(segmentedContainerClasses, className)}
      {...props}
    />
  );
}

function TabsTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        segmentedItemBaseClasses,
        segmentedItemStateClasses,
        className,
      )}
      {...props}
    >
      <div className="w-full">{children}</div>
    </TabsPrimitive.Trigger>
  );
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
