"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
// @ts-ignore
import Image from "next/image";

export interface WalletButtonProps {
  /** Whether the wallet button is disabled */
  disabled?: boolean;
  /** Click handler for the wallet button */
  onClick?: () => void;
  /** Custom className for styling */
  className?: string;
  /** Size variant for the button */
  size?: "default" | "small" | "icon";
  /** Whether to show the wallet icon */
  showIcon?: boolean;
  /** Custom wallet icon path */
  iconPath?: string;
  /** But<PERSON> text - defaults to "Cüzdan" */
  children?: React.ReactNode;
}

export function WalletButton({
  disabled = false,
  onClick,
  className,
  size = "default",
  showIcon = true,
  iconPath = "/assets/wallet-lg.png",
  children = "Cüzdan",
}: WalletButtonProps) {
  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      size={size}
      variant="default"
      className={`flex items-center gap-2 ${className || ""}`}
    >
      {showIcon && (
        <Image
          src={iconPath}
          alt="Wallet"
          width={20}
          height={20}
          className="w-5 h-5"
        />
      )}
      {children}
    </Button>
  );
}
