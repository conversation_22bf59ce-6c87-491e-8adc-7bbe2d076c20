"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";

// Types for step configuration
export interface StepConfig {
  id: string;
  label: string;
  disabled?: boolean;
}

// Context for sharing stepper state
interface StepperContextValue {
  currentStep: number;
  totalSteps: number;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (stepIndex: number) => void;
  canGoNext: boolean;
  canGoPrev: boolean;
  isStepActive: (stepIndex: number) => boolean;
  isStepCompleted: (stepIndex: number) => boolean;
  steps: StepConfig[];
}

const StepperContext = React.createContext<StepperContextValue | null>(null);

function useStepperContext() {
  const context = React.useContext(StepperContext);
  if (!context) {
    throw new Error("Stepper components must be used within a StepperVertical");
  }
  return context;
}

// Circle indicator variants
const stepCircleVariants = cva(
  "flex items-center justify-center rounded-full border-2 transition-all duration-200 text-sm font-bold",
  {
    variants: {
      state: {
        active: "bg-foreground text-background border-foreground",
        inactive: "bg-muted text-muted-foreground border-muted-foreground",
        completed: "bg-foreground text-background border-foreground",
      },
      size: {
        default: "size-8",
        large: "size-10 text-base",
      },
    },
    defaultVariants: {
      state: "inactive",
      size: "default",
    },
  },
);

// Line connector variants
const stepLineVariants = cva("w-0.5 transition-all duration-200", {
  variants: {
    state: {
      active: "bg-foreground",
      inactive: "bg-muted-foreground",
      completed: "bg-foreground",
    },
  },
  defaultVariants: {
    state: "inactive",
  },
});

// Main Stepper Component
interface StepperVerticalProps {
  steps: StepConfig[];
  currentStep?: number;
  onStepChange?: (stepIndex: number) => void;
  className?: string;
  children?: React.ReactNode;
  allowSkipping?: boolean; // Allow jumping to any step
}

function StepperVertical({
  steps,
  currentStep = 0,
  onStepChange,
  className,
  children,
  allowSkipping = true,
}: StepperVerticalProps) {
  const [internalStep, setInternalStep] = React.useState(currentStep);

  // Use internal state if no external control is provided
  const activeStep = onStepChange ? currentStep : internalStep;

  // We're managing our own state instead of using headless-stepper

  const goToStep = React.useCallback(
    (stepIndex: number) => {
      if (
        stepIndex >= 0 &&
        stepIndex < steps.length &&
        !steps[stepIndex]?.disabled
      ) {
        if (
          allowSkipping ||
          stepIndex <= activeStep + 1 ||
          stepIndex < activeStep
        ) {
          if (onStepChange) {
            onStepChange(stepIndex);
          } else {
            setInternalStep(stepIndex);
          }
        }
      }
    },
    [steps, onStepChange, allowSkipping, activeStep],
  );

  const contextValue: StepperContextValue = React.useMemo(
    () => ({
      currentStep: activeStep,
      totalSteps: steps.length,
      nextStep: () => {
        const nextStepIndex = activeStep + 1;
        if (nextStepIndex < steps.length) {
          goToStep(nextStepIndex);
        }
      },
      prevStep: () => {
        const prevStepIndex = activeStep - 1;
        if (prevStepIndex >= 0) {
          goToStep(prevStepIndex);
        }
      },
      goToStep,
      canGoNext: activeStep < steps.length - 1,
      canGoPrev: activeStep > 0,
      isStepActive: (stepIndex: number) => stepIndex === activeStep,
      isStepCompleted: (stepIndex: number) => stepIndex < activeStep,
      steps,
    }),
    [activeStep, steps, goToStep],
  );

  return (
    <StepperContext.Provider value={contextValue}>
      <div className={cn("flex flex-col", className)}>{children}</div>
    </StepperContext.Provider>
  );
}

// Step Navigation Component
interface StepNavigationProps {
  className?: string;
  size?: VariantProps<typeof stepCircleVariants>["size"];
}

function StepNavigation({ className, size = "default" }: StepNavigationProps) {
  const { steps, isStepActive, isStepCompleted, goToStep } =
    useStepperContext();

  return (
    <nav className={cn("flex flex-col", className)}>
      {steps.map((step, index) => {
        const isActive = isStepActive(index);
        const isCompleted = isStepCompleted(index);
        const state = isActive
          ? "active"
          : isCompleted
            ? "completed"
            : "inactive";
        const isLast = index === steps.length - 1;

        return (
          <div key={step.id} className="flex items-start">
            {/* Navigation Circle and Line */}
            <div className="flex flex-col items-center">
              <button
                onClick={() => goToStep(index)}
                disabled={step.disabled}
                className={cn(
                  stepCircleVariants({ state, size }),
                  "hover:scale-105 disabled:cursor-not-allowed disabled:hover:scale-100 shrink-0",
                )}
                aria-label={`Adım ${index + 1}`}
              >
                {isCompleted ? "✓" : index + 1}
              </button>
              {!isLast && (
                <div
                  className={cn(
                    stepLineVariants({
                      state: isCompleted ? "completed" : "inactive",
                    }),
                    "w-0.5 flex-1 mt-2",
                  )}
                />
              )}
            </div>

            {/* Content Area */}
            <div className="flex-1 ml-4 pb-4">
              <StepContentActive stepId={step.id}>
                <div>{/* Active content will be rendered here */}</div>
              </StepContentActive>
              <StepContentInactive stepId={step.id}>
                <div>{/* Inactive content will be rendered here */}</div>
              </StepContentInactive>
            </div>
          </div>
        );
      })}
    </nav>
  );
}

// Previous Button
interface StepPreviousButtonProps {
  className?: string;
  children?: React.ReactNode;
}

function StepPreviousButton({
  className,
  children = "Önceki",
}: StepPreviousButtonProps) {
  const { prevStep, canGoPrev } = useStepperContext();

  return (
    <Button
      onClick={prevStep}
      disabled={!canGoPrev}
      variant="secondary"
      size="small"
      className={className}
    >
      {children}
    </Button>
  );
}

// Next Button
interface StepNextButtonProps {
  className?: string;
  children?: React.ReactNode;
}

function StepNextButton({
  className,
  children = "Sonraki",
}: StepNextButtonProps) {
  const { nextStep, canGoNext } = useStepperContext();

  return (
    <Button
      onClick={nextStep}
      disabled={!canGoNext}
      variant="primary"
      size="small"
      className={className}
    >
      {children}
    </Button>
  );
}

// Step Activation Button
interface StepActivationButtonProps {
  stepId: string;
  children: React.ReactNode;
  className?: string;
}

function StepActivationButton({
  stepId,
  children,
  className,
}: StepActivationButtonProps) {
  const { steps, goToStep } = useStepperContext();

  const stepIndex = steps.findIndex((step) => step.id === stepId);
  const step = steps[stepIndex];

  if (stepIndex === -1) {
    console.warn(`Step with id "${stepId}" not found`);
    return null;
  }

  return (
    <Button
      onClick={() => goToStep(stepIndex)}
      disabled={step?.disabled}
      className={className}
      size="small"
    >
      {children}
    </Button>
  );
}

// Step Content Components
interface StepContentProps {
  stepId: string;
  children: React.ReactNode;
  className?: string;
}

function StepContentActive({ stepId, children, className }: StepContentProps) {
  const { steps, currentStep } = useStepperContext();
  const stepIndex = steps.findIndex((step) => step.id === stepId);

  if (stepIndex !== currentStep) {
    return null;
  }

  return (
    <div className={cn("animate-in fade-in-50 duration-200", className)}>
      {children}
    </div>
  );
}

function StepContentInactive({
  stepId,
  children,
  className,
}: StepContentProps) {
  const { steps, currentStep } = useStepperContext();
  const stepIndex = steps.findIndex((step) => step.id === stepId);

  if (stepIndex === currentStep) {
    return null;
  }

  return <div className={cn("", className)}>{children}</div>;
}

export {
  StepperVertical,
  StepNavigation,
  StepPreviousButton,
  StepNextButton,
  StepActivationButton,
  StepContentActive,
  StepContentInactive,
};
